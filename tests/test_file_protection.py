"""
Test file protection mechanisms to ensure MCP doesn't scan:
1. Its own directory
2. User's home directory root
3. Excluded directories
"""

from pathlib import Path
from unittest.mock import patch

from utils.file_utils import (
    expand_paths,
    get_user_home_directory,
    is_home_directory_root,
    is_mcp_directory,
)


class TestMCPDirectoryDetection:
    """Test MCP self-detection to prevent scanning its own code."""

    def test_detect_mcp_directory_dynamically(self, tmp_path):
        """Test dynamic MCP directory detection based on script location."""
        # The is_mcp_directory function now uses __file__ to detect MCP location
        # It checks if the given path is a subdirectory of the MCP server
        from pathlib import Path

        import utils.file_utils

        # Get the actual MCP server directory
        mcp_server_dir = Path(utils.file_utils.__file__).parent.parent.resolve()

        # Test that the MCP server directory itself is detected
        assert is_mcp_directory(mcp_server_dir) is True

        # Test that a subdirectory of MCP is also detected
        if (mcp_server_dir / "tools").exists():
            assert is_mcp_directory(mcp_server_dir / "tools") is True

    def test_no_detection_on_non_mcp_directory(self, tmp_path):
        """Test no detection on directories outside MCP."""
        # Any directory outside the MCP server should not be detected
        non_mcp_dir = tmp_path / "some_other_project"
        non_mcp_dir.mkdir()

        assert is_mcp_directory(non_mcp_dir) is False

    def test_no_detection_on_regular_directory(self, tmp_path):
        """Test no detection on regular project directories."""
        # Create some random Python files
        (tmp_path / "app.py").touch()
        (tmp_path / "main.py").touch()
        (tmp_path / "utils.py").touch()

        assert is_mcp_directory(tmp_path) is False

    def test_no_detection_on_file(self, tmp_path):
        """Test no detection when path is a file, not directory."""
        file_path = tmp_path / "test.py"
        file_path.touch()

        assert is_mcp_directory(file_path) is False

    def test_mcp_directory_excluded_from_scan(self, tmp_path):
        """Test that MCP directories are excluded during path expansion."""
        # For this test, we need to mock is_mcp_directory since we can't
        # actually create the MCP directory structure in tmp_path
        from unittest.mock import patch as mock_patch

        # Create a project with a subdirectory we'll pretend is MCP
        project_root = tmp_path / "my_project"
        project_root.mkdir()

        # Add some project files
        (project_root / "app.py").write_text("# My app")
        (project_root / "config.py").write_text("# Config")

        # Create a subdirectory that we'll mock as MCP
        fake_mcp_dir = project_root / "gemini-mcp-server"
        fake_mcp_dir.mkdir()
        (fake_mcp_dir / "server.py").write_text("# MCP server")
        (fake_mcp_dir / "test.py").write_text("# Should not be included")

        # Mock is_mcp_directory to return True for our fake MCP dir
        def mock_is_mcp(path):
            return str(path).endswith("gemini-mcp-server")

        # Scan the project with mocked MCP detection
        with mock_patch("utils.file_utils.is_mcp_directory", side_effect=mock_is_mcp):
            files = expand_paths([str(project_root)])

        # Verify project files are included but MCP files are not
        file_names = [Path(f).name for f in files]
        assert "app.py" in file_names
        assert "config.py" in file_names
        assert "test.py" not in file_names  # From MCP dir
        assert "server.py" not in file_names  # From MCP dir


class TestHomeDirectoryProtection:
    """Test protection against scanning user's home directory root."""

    def test_detect_exact_home_directory(self):
        """Test detection of exact home directory path."""
        with patch("utils.file_utils.get_user_home_directory") as mock_home:
            mock_home.return_value = Path("/Users/<USER>")

            assert is_home_directory_root(Path("/Users/<USER>")) is True
            assert is_home_directory_root(Path("/Users/<USER>/")) is True

    def test_allow_home_subdirectories(self):
        """Test that subdirectories of home are allowed."""
        with patch("utils.file_utils.get_user_home_directory") as mock_home:
            mock_home.return_value = Path("/Users/<USER>")

            assert is_home_directory_root(Path("/Users/<USER>/projects")) is False
            assert is_home_directory_root(Path("/Users/<USER>/Documents/code")) is False

    def test_detect_home_patterns_macos(self):
        """Test detection of macOS home directory patterns."""
        # Test various macOS home patterns
        assert is_home_directory_root(Path("/Users/<USER>")) is True
        assert is_home_directory_root(Path("/Users/<USER>")) is True
        # But subdirectories should be allowed
        assert is_home_directory_root(Path("/Users/<USER>/projects")) is False

    def test_detect_home_patterns_linux(self):
        """Test detection of Linux home directory patterns."""
        assert is_home_directory_root(Path("/home/<USER>")) is True
        assert is_home_directory_root(Path("/home/<USER>")) is True
        # But subdirectories should be allowed
        assert is_home_directory_root(Path("/home/<USER>/code")) is False

    def test_detect_home_patterns_windows(self):
        """Test detection of Windows home directory patterns."""
        assert is_home_directory_root(Path("C:\\Users\\<USER>\\Users\\John\\Documents")) is False

    def test_home_directory_excluded_from_scan(self, tmp_path):
        """Test that home directory root is excluded during path expansion."""
        with patch("utils.file_utils.get_user_home_directory") as mock_home:
            mock_home.return_value = tmp_path
            # Try to scan home directory
            files = expand_paths([str(tmp_path)])
            # Should return empty as home root is skipped
            assert files == []


class TestUserHomeEnvironmentVariable:
    """Test USER_HOME environment variable handling."""

    def test_user_home_from_pathlib(self):
        """Test that get_user_home_directory uses Path.home()."""
        with patch("pathlib.Path.home") as mock_home:
            mock_home.return_value = Path("/Users/<USER>")
            home = get_user_home_directory()
            assert home == Path("/Users/<USER>")

    def test_get_home_directory_uses_pathlib(self):
        """Test that get_user_home_directory always uses Path.home()."""
        with patch("pathlib.Path.home") as mock_home:
            mock_home.return_value = Path("/home/<USER>")
            home = get_user_home_directory()
            assert home == Path("/home/<USER>")
            # Verify Path.home() was called
            mock_home.assert_called_once()

    def test_home_directory_on_different_platforms(self):
        """Test home directory detection on different platforms."""
        # Test different platform home directories
        test_homes = [
            Path("/Users/<USER>"),  # macOS
            Path("/home/<USER>"),  # Linux
            Path("C:\\Users\\<USER>