# challenge - Challenge an approach or validate ideas with confidence

The `challenge` tool encourages thoughtful critical thinking instead of automatic agreement with the dreaded **You're absolutely right!** responses - especially 
when you're not. This tool wraps your comment with instructions that prompt critical thinking and honest analysis instead of blind agreement.

## Quick Example

```
challenge but do we even need all this extra caching because it'll just slow the app down?
```

```
challenge I don't think this approach solves my original complaint
```

Normally, your favorite coding agent will enthusiastically reply with **“You’re absolutely right!”**—then proceed to 
reverse the _correct_ strategy entirely, without stopping to consider that you might actually be wrong, missing the 
bigger picture or ignoring architectural constraints.

`challenge` fixes this. <PERSON> can even _detect_ when you're challenging something and automatically invokes this tool
to ensure thoughtful analysis instead of reflexive agreement.

**Without Zen:**
![without_zen@2x](https://github.com/user-attachments/assets/64f3c9fb-7ca9-4876-b687-25e847edfd87)

**With Zen:**
![with_zen@2x](https://github.com/user-attachments/assets/9d72f444-ba53-4ab1-83e5-250062c6ee70)

## Why Use Challenge?

AI assistants sometimes tend to agree too readily. The challenge tool helps you:
- Get genuine critical evaluation of your ideas
- Challenge assumptions constructively
- Receive honest feedback on proposals
- Validate approaches with thoughtful analysis