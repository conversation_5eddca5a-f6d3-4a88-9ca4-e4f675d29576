[project]
name = "zen-mcp-server"
version = "0.1.0"
description = "AI-powered MCP server with multiple model providers"
requires-python = ">=3.9"
dependencies = [
    "mcp>=1.0.0",
    "google-genai>=1.19.0",
    "openai>=1.55.2",
    "pydantic>=2.0.0",
    "python-dotenv>=1.0.0",
]

[tool.setuptools.packages.find]
include = ["tools*", "providers*", "systemprompts*", "utils*"]

[tool.setuptools]
py-modules = ["server", "config"]

[tool.setuptools.package-data]
"*" = ["conf/*.json"]

[tool.setuptools.data-files]
"conf" = ["conf/custom_models.json"]

[project.scripts]
zen-mcp-server = "server:run"

[tool.black]
line-length = 120
target-version = ['py39', 'py310', 'py311', 'py312', 'py313']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | venv
  | _build
  | buck-out
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
include_trailing_comma = true
force_grid_wrap = 0
use_parentheses = true
ensure_newline_before_comments = true
line_length = 120
skip_glob = ["venv/*", ".venv/*", ".zen_venv/*"]

[tool.ruff]
target-version = "py39"
line-length = 120

[tool.ruff.lint]
select = [
    "E",  # pycodestyle errors
    "W",  # pycodestyle warnings
    "F",  # pyflakes
    "I",  # isort
    "B",  # flake8-bugbear
    "C4", # flake8-comprehensions
    "UP", # pyupgrade
]
ignore = [
    "E501",  # line too long, handled by black
    "B008",  # do not perform function calls in argument defaults
    "C901",  # too complex
    "B904",  # exception handling with raise from
]

[tool.ruff.lint.per-file-ignores]
"__init__.py" = ["F401"]
"tests/*" = ["B011"]
"tests/conftest.py" = ["E402"]  # Module level imports not at top of file - needed for test setup

[build-system]
requires = ["setuptools>=45", "wheel", "setuptools_scm[toml]>=6.2"]
build-backend = "setuptools.build_meta"
